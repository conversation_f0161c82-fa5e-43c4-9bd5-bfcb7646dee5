## For storing the common configs
import pytz
from main.enums import StorageType, Check
from dataclasses import dataclass, field
from typing import Any, Callable, Callable, List, Dict, Tuple, Union
from operator import mul, truediv
import pandas as pd
from numpy.typing import DTypeLike


@dataclass
class ConfigBase:
    def __post_init__(self) -> None:
        self.DATE_TODAY = (
            pd.Timestamp.now(tz=self.TIMEZONE).replace(tzinfo=None).normalize()
        )

    ALLOWED_OPERATIONS: List[str] = field(
        default_factory=lambda: [
            "OI",
            "pcr",
            "iv",
            "delta",
            "gamma",
            "vega",
            "theta",
        ]
    )  ## more corpacts related ops to be added
    STORE_MAP: Dict[str, StorageType] = field(
        default_factory=lambda: {
            "db": StorageType.DB,
            "file": StorageType.FILE,
            "local": StorageType.LOCAL,
        }
    )

    OPTION_LIST: List[str] = field(default_factory=lambda: [])
    FUTURE_LIST: List[str] = field(default_factory=lambda: [])
    SPOT_LIST: List[str] = field(default_factory=lambda: [])

    DATA_TYPES_DICT: Dict[Union[str, int], DTypeLike] = field(
        default_factory=lambda: {
            "ID": "uint64",
            "Symbol": "object",
            "symbol": "object",
            "upper_circuit": "float64",
            "lower_circuit": "float64",
            "date": "datetime64[ns]",
            "exdate": "datetime64[ns]",
            "Open": "float64",
            "Open_int": "float64",
            "open_interest": "float64",
            "open interest(lots)": "int64",
            "High": "float64",
            "Low": "float64",
            "Close": "float64",
            "timestamp": "datetime64[ns]",
            "iv": "float64",
            "Vwap": "float64",
            "Cons_Volume": "float64",
            "Next_Cons_Volume": "float64",
            "expiry_date": "datetime64[ns]",
            "option_type": "int16",
            "option type": "int16",
            "option": "int64",
            "strike": "float64",
            "strike price": "float64",
            "sym_id": "int32",
            "slippage": "float64",
            "return": "float64",
            "nw": "float64",
            "dayT": "float64",
            "Ltv": "float64",
            "pcr": "float64",
            "actual_date": "datetime64[ns]",
            "expiry": "datetime64[ns]",
            "OI": "float64",
            "sector": "object",
            "Close_wgts": "float64",
            "vol_wgts": "float64",
            "Client": "float64",
            "DII": "float64",
            "FII": "float64",
            "Pro": "float64",
            "Total": "float64",
            "segment_type": "object",
            "long_margin": "float64",
            "short_margin": "float64",
            "segment": "object",
            "buy_contract": "float64",
            "buy_value": "float64",
            "sell_contract": "float64",
            "sell_value": "float64",
            "oi_contract": "float64",
            "oi_value": "float64",
            "span": "float64",
            "exposure": "float64",
            "fii_grosspurchase": "float64",
            "fii_grosssales": "float64",
            "fii_net": "float64",
            "dii_grosspurchase": "float64",
            "dii_grosssales": "float64",
            "dii_net": "float64",
            "contract_price": "float64",
            "delta": "float64",
            "gamma": "float64",
            "theta": "float64",
            "vega": "float64",
            "R1": "float64",
            "R2": "float64",
            "R3": "float64",
            "R4": "float64",
            "R5": "float64",
            "R6": "float64",
            "R7": "float64",
            "R8": "float64",
            "R9": "float64",
            "R10": "float64",
            "R11": "float64",
            "R12": "float64",
            "R13": "float64",
            "R14": "float64",
            "R15": "float64",
            "R16": "float64",
            "symbol": "object",
            "series": "object",
            "open": "float64",
            "high": "float64",
            "low": "float64",
            "close": "float64",
            "last": "float64",
            "prevclose": "float64",
            "tottrdqty": "int64",
            "tottrdval": "float64",
            "totaltrades": "float64",
            "isin": "object",
            "balte_id": "uint64",
            "close_raw": "float64",
            "eod_price": "float64",
            "adj_factor": "float64",
            "instrument": "object",
            "instrument_name": "object",
            "instrument name": "object",
            "expiry_dt": "datetime64[ns]",
            "expiry date": "datetime64[ns]",
            "strike_pr": "float64",
            "strike_price": "float64",
            "option_typ": "object",
            "settle_pr": "float64",
            "contracts": "float64",
            "val_inlakh": "float64",
            "open_int": "float64",
            "chg_in_oi": "float64",
            "expiry_rank": "float64",
            "repo_rate": "float64",
            "contract_d": "object",
            "previous_s": "float64",
            "previous close": "float64",
            "open_price": "float64",
            "high_price": "float64",
            "low_price": "float64",
            "close_pric": "float64",
            "settlement": "float64",
            "net_change": "float64",
            "oi_no_con": "float64",
            "traded_qua": "int64",
            "trd_no_con": "int64",
            "traded_val": "float64",
            "undrlng_st": "int64",
            "notional_v": "int64",
            "premium_tr": "int64",
            "pb": "float64",
            "divyield": "float64",
            "turnover": "float64",
            "turnover_in_lacs": "float64",
            "value(lacs)": "float64",
            "pe": "float64",
            "volume": "float64",
            "near_week": "int32",
            "near_month": "int32",
            "next_week": "int32",
            "next_month": "int32",
            "far_week": "int32",
            "far_month": "int32",
            "0": "int64",
            0: "float64",
            "session": "uint16",
            "session_date": "datetime64[ns]",
            "tender": "float64",
            "volume_in_lots": "int64",
            "volume(lots)": "int64",
            "volume(in 000's)": "float64",
            "volume_in_thousands": "float64",
            "delivery": "float64",
            "min_price": "float64",
            "additional_margin": "float64",
            "side": "uint8",
            "level": "uint32",
            "price": "uint32",
            "quantity": "uint64",
            "queue_depth": "uint32",
        }
    )

    SAMPLING_AGG_DICT: Dict[str, str] = field(
        default_factory=lambda: {
            "Open": "first",
            "High": "max",
            "Low": "min",
            "Close": "last",
            "Vwap": "last",
            "Cons_Volume": "last",
            "Next_Cons_Volume": "last",
            "OI": "last",
            "pcr": "last",
            "Open_int": "last",
            "iv": "last",
            "delta": "last",
            "gamma": "last",
            "theta": "last",
            "vega": "last",
            "sym_id": "last",
            "strike": "last",
            "option_type": "last",
            "expiry_date": "last",
            "session": "last",
            "session_date": "last",
            "Vwap": "last",
            "pcr": "last",
            "Ltv": "last",
        }
    )

    UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST: Dict[str, List[str]] = field(
        default_factory=dict
    )
    UNIVERSE_TO_MINIO_FILES_LIST: Dict[str, Dict[str, str]] = field(
        default_factory=dict
    )
    UNIVERSE_TO_BALTE_UNIVERSE_MAPPING: Dict[str, str] = field(default_factory=dict)
    UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT: Dict[
        str, Dict[Union[str, int], str]
    ] = field(default_factory=dict)
    UNIVERSE_TO_IGNORE_CHECK_LIST: Dict[str, List[Check]] = field(default_factory=dict)
    UNIVERSE_TO_CHECK_LIST: Dict[str, List[Check]] = field(default_factory=dict)

    COLUMN_JUMPS_CHECK = ["Open", "Low", "High", "Close"]
    COLUMN_FFILL_CHECK = ["OI", "Open_int"]
    COLUMN_MONOTONIC_CHECK = ["index", "Cons_Volume"]

    ALLOWED_SUDDEN_JUMP = -0.1

    DEFAULT_NAN_RATIO = -0.1

    ALLOWED_NAN_INCREASE = 0.1
    ALLOWED_JUMP_INCREASE = 0.05

    ALLOWED_OHLC_PERCENTAGE = 100

    ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER = 10
    ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER = 10
    ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER = 10

    DATA_JUMPS_THRESHOLD = 0.0

    TOTAL_CHECKS = 11
    META_DATA_LIBRARY = "metadata"

    INTEREST_RATE = 0.1
    EXPIRY_TIME = 55800

    EXCHANGE = ""
    FILE_STORAGE = ""
    DB_STORAGE = ""

    FILE_DICT: Dict[str, Tuple[str, str]] = field(default_factory=dict)
    MARKET_HOURS_DICT: Dict[str, Dict[str, str]] = field(default_factory=dict)

    COLUMNS_DICT: Dict[str, List[str]] = field(default_factory=dict)
    UNIVERSE_DTYPE_TO_COLUMN_DICT: Dict[str, List[str]] = field(default_factory=dict)
    UNIVERSE_TO_RENAME_COLUMN_DICT: Dict[str, Dict[Union[int, str], str]] = field(
        default_factory=dict
    )
    PRE_DEPENDENCIES: Dict[str, List[str]] = field(default_factory=dict)
    POST_DEPENDENCIES: Dict[str, List[str]] = field(default_factory=dict)
    REQUIRED_OPERATION: Dict[str, List[str]] = field(default_factory=dict)

    OPTION_TO_UNDERLYING_MAP: Dict[str, str] = field(default_factory=dict)
    FUTURE_TO_UNDERLYING_MAP: Dict[str, str] = field(default_factory=dict)

    UNDERLYING_TO_OPTION_MAP: Dict[str, str] = field(default_factory=dict)
    UNDERLYING_TO_FUTURE_MAP: Dict[str, str] = field(default_factory=dict)

    LOCAL_FILE_LOCATION = ""

    MODIFY_COLUMNS_OPERATIONS: Dict[str, List[str]] = field(default_factory=dict)
    FILTER_DATA_OPERATIONS: Dict[str, List[str]] = field(default_factory=dict)
    SERIES_FILTER_MAP: Dict[str, List[str]] = field(default_factory=dict)
    CORPACT_ADJUSTMENT_ACTIONS: Dict[
        str,
        Callable[
            [Union[float, "pd.Series[float]"], Union[float, "pd.Series[float]"]],
            Union[float, "pd.Series[float]"],
        ],
    ] = field(
        default_factory=lambda: {
            "Open": mul,
            "High": mul,
            "Low": mul,
            "Close": mul,
            "Vwap": mul,
            "Cons_Volume": truediv,
            "Next_Cons_Volume": truediv,
            "fut_close": mul,
            "near_month": truediv,
            "next_month": truediv,
            "far_month": truediv,
            "upper_circuit": mul,
            "lower_circuit": mul,
            "Close_wgts": truediv,
            "vol_wgts": mul,
            "open": mul,
            "high": mul,
            "low": mul,
            "close": mul,
            "adj_factor": mul,
            "eod_price": mul,
        }
    )

    BASIS_ADJUSTMENT_COLUMNS: List[str] = field(
        default_factory=lambda: ["Open", "High", "Low", "Close", "Vwap"]
    )
    TIMEZONE = pytz.timezone("Asia/Kolkata")
    DATE_TODAY: pd.Timestamp = pd.Timestamp.now().normalize()

    AFTER_MARKET_DICT_MINIO: Dict[str, str] = field(default_factory=dict)
    AFTER_MARKET_DICT_LIST: List[str] = field(default_factory=list)
    CORPACT_UNIVERSES: List[str] = field(default_factory=list)
    CORPACT_LIBRARIES: List[str] = field(default_factory=list)
    AFTER_MARKET_CORPACTS_UNIVERSES: List[str] = field(default_factory=list)
    SEGMENT_APPEND_LIST: List[Tuple[str, int, str]] = field(default_factory=list)
    UNIVERSE_TO_LOTSIZE_DICT: Dict[str, str] = field(default_factory=dict)
